import { Typography } from '@ds';
import dayjs from 'dayjs';
import BeneficialOwnersReportFiltersModal from '@/components/investors/beneficial-owners/reports/filters/modal';
import { useBoReportTableContext } from '@/components/investors/beneficial-owners/reports/report-context';
import { useBoTradingActivityTableContext } from '@/components/investors/beneficial-owners/reports/trading-activity/context';
import BoReportTableFilterChips from '@/components/investors/beneficial-owners/table/filter-chips';

const ReportTableHeader = () => {
  const { data } = useBoReportTableContext();

  const {
    postFilteringStats: { totalChange, totalShares },
  } = useBoTradingActivityTableContext();

  // if (totalChange === totalShares) {
  //   return null;
  // }

  return (
    <>
      <div className="relative flex h-full items-center justify-between gap-4 p-4 pr-6">
        <div>
          <Typography
            className="mb-1 min-w-[240px] shrink-0 max-md:max-w-full"
            variant="text-heading-sm"
          >
            Trading activity by linked contact
          </Typography>
          {data?.beneficialOwnersReport.metadata?.previousReportDate && (
            <Typography className="text-gray-500" variant="text-body-sm">
              {`Movements aggregated at the contact level between ${dayjs(
                data.beneficialOwnersReport.metadata.previousReportDate
              ).format('DD MMM YYYY')} and ${dayjs(
                data.beneficialOwnersReport.reportDate
              ).format('DD MMM YYYY')}`}
            </Typography>
          )}
        </div>
        <div className="hidden items-center sm:flex">
          <BeneficialOwnersReportFiltersModal />
        </div>
      </div>
      <BoReportTableFilterChips />
    </>
  );
};

export default ReportTableHeader;
