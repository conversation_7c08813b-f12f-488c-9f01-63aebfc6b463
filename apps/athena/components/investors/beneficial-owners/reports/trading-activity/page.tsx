import { FC } from 'react';

import { BoByContactTableProvider } from '@/components/investors/beneficial-owners/reports/by-contact/context';
import { BoTradingActivityTableProvider } from '@/components/investors/beneficial-owners/reports/trading-activity/context';
import TradingActivityStats from '@/components/investors/beneficial-owners/reports/trading-activity/stats';
import TradingActivityTable from '@/components/investors/beneficial-owners/reports/trading-activity/table';
import ReportTableHeader from '@/components/investors/beneficial-owners/reports/trading-activity/table-header';

const TradingActivityPage: FC = () => {
  return (
    <BoByContactTableProvider>
      <BoTradingActivityTableProvider>
        <div className="flex flex-col gap-6">
          <TradingActivityStats />
          <div className="rounded-lg border bg-white">
            <ReportTableHeader />

            <TradingActivityTable />
          </div>
        </div>
      </BoTradingActivityTableProvider>
    </BoByContactTableProvider>
  );
};

export default TradingActivityPage;
