import { FC, useState, useMemo } from 'react';
import { Spinner, Typography } from '@ds';
import dayjs from 'dayjs';
import numeral from 'numeral';
import { useBoReportTableContext } from '@/components/investors/beneficial-owners/reports/report-context';
import { useBoTradingActivityTableContext } from '@/components/investors/beneficial-owners/reports/trading-activity/context';
import TradingActivityRow from '@/components/investors/beneficial-owners/reports/trading-activity/row';
import TableEmpty from '@/components/utils/tables/empty';
import TableHeader, { Header } from '@/components/utils/tables/table-header';

const TradingActivityTable: FC = () => {
  const {
    error,
    filters,
    loading,
    previousReportDate,
    search,
    setFilters,
    setSearch,
  } = useBoReportTableContext();
  const {
    filteredParticipantsByContact,
    postFilteringStats: {
      totalChange,
      totalContacts,
      totalParticipantsUnderContacts,
      totalPortion,
      totalShares,
    },
  } = useBoTradingActivityTableContext();

  const changedSinceTitle = useMemo(
    () =>
      previousReportDate
        ? `Change since ${dayjs(previousReportDate).format('DD/MM/YYYY')}`
        : 'Change since last report',
    [previousReportDate]
  );

  const columns: Header[] = [
    {
      className:
        'sm:w-[64px] min-w-[60px] max-w-[68px] whitespace-nowrap lg:pl-3',
      label: 'Rank',
    },
    {
      className: 'sm:w-[700px] min-w-[250px] whitespace-nowrap',
      label: 'Contact name',
    },
    {
      className: 'sm:w-[100px] min-w-[100px] text-right',
      label: 'Shares (#)',
      value: 'shares',
    },
    {
      className: 'sm:w-[100px] min-w-[100px] text-right',
      label: '% of total holdings',
    },
    {
      className: 'sm:w-[100px] min-w-[100px] text-right',
      label: changedSinceTitle,
    },
    {
      className: 'text-right sm:w-[120px] max-w-[160px]',
      label: 'Movement Type',
    },
  ];
  const sortableColumns = [
    'Shares (#)',
    '% of total holdings',
    changedSinceTitle,
  ];
  const [sortedColumn, setSortedColumn] = useState({
    column: 'Shares (#)',
    desc: true,
  });

  const participantsSorted = useMemo(() => {
    const sorted = [...filteredParticipantsByContact];
    const { column, desc } = sortedColumn;
    if (column === 'Shares (#)') {
      sorted.sort(({ contact: { shares: a } }, { contact: { shares: b } }) =>
        desc ? (b || 0) - (a || 0) : (a || 0) - (b || 0)
      );
    }
    if (column === '% of total holdings') {
      sorted.sort(
        (
          { contact: { portionOfTotal: a } },
          { contact: { portionOfTotal: b } }
        ) => (desc ? (b || 0) - (a || 0) : (a || 0) - (b || 0))
      );
    }
    if (column === changedSinceTitle) {
      sorted.sort(
        (
          { contact: { changeSinceLastReport: a } },
          { contact: { changeSinceLastReport: b } }
        ) => {
          const aValue = Math.abs(typeof a === 'number' ? a : Number(a) || 0);
          const bValue = Math.abs(typeof b === 'number' ? b : Number(b) || 0);

          return desc ? bValue - aValue : aValue - bValue;
        }
      );
    }
    return sorted;
  }, [filteredParticipantsByContact, sortedColumn, changedSinceTitle]);

  const sortBy = (header: string) => {
    const headerColumn = columns.find((h) => h.label === header);

    if (!headerColumn || !sortableColumns || sortableColumns.length == 0)
      return;

    if (sortedColumn && sortedColumn.column === header) {
      setSortedColumn({ column: headerColumn.label, desc: !sortedColumn.desc });
    }
    // If not sorted by this column yet, start with descending
    else {
      setSortedColumn({ column: headerColumn.label, desc: true });
    }
  };

  if (loading) {
    return (
      <div className="flex h-[540px] flex-col items-center justify-center space-y-3">
        <Spinner size="lg" />
        <Typography className="text-gray-500" variant="text-body-md">
          Loading participants...
        </Typography>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-[340px] flex-col items-center justify-center space-y-3">
        <Typography className="text-red-500" variant="text-body-md">
          Error loading report: {error.message}
        </Typography>
      </div>
    );
  }

  // if (totalChange === totalShares) {
  //   return <EmptyState />;
  // }

  return (
    <div>
      <div className="sticky top-[55px] z-[1] md:border-b" />
      <div className="overflow-x-auto rounded-b-lg">
        <table className="w-full divide-y divide-gray-200">
          <TableHeader
            headers={columns}
            sortBy={sortBy}
            sortableHeaders={sortableColumns}
            sortedColumn={sortedColumn}
            textColor="text-gray-500"
            textVariant="text-button-sm"
          />
          <tbody className="divide-y divide-gray-200 bg-white">
            {participantsSorted.map((participant) => (
              <TradingActivityRow
                key={`contact-row-${participant.contact.id}`}
                row={participant}
              />
            ))}
            {participantsSorted.length === 0 && (
              <>
                {filters.length > 0 || (search && search !== '-') ? (
                  <TableEmpty
                    clearFilters={() => {
                      setSearch('');
                      setFilters([]);
                    }}
                    columnCount={columns.length}
                    message="No owners found"
                  />
                ) : (
                  <TableEmpty
                    columnCount={columns.length}
                    message="Report is empty"
                    secondaryMessage="We have not uncovered owners for this report yet."
                  />
                )}
              </>
            )}
          </tbody>
          {participantsSorted.length > 0 && (
            <tfoot>
              <tr>
                <td className="p-4 pr-3">
                  <Typography variant="text-label-sm">Totals</Typography>
                </td>
                <td className="px-3">
                  <Typography variant="text-label-sm">{`${
                    totalContacts || 0
                  } contacts, ${numeral(totalParticipantsUnderContacts).format(
                    '0,0'
                  )} accounts`}</Typography>
                </td>
                <td className="px-3 text-right">
                  <Typography variant="text-label-sm">
                    {numeral(totalShares).format('0,0')}
                  </Typography>
                </td>
                <td className="px-3 text-right">
                  <Typography variant="text-label-sm">
                    {numeral(totalPortion).format('0.00%')}
                  </Typography>
                </td>
                <td className="px-3 text-right">
                  {totalChange > 0 ? (
                    <Typography
                      className="text-green-600"
                      variant="text-body-sm"
                    >
                      {`+ ${numeral(totalChange).format('0,0')}`}
                    </Typography>
                  ) : totalChange < 0 ? (
                    <Typography className="text-red-600" variant="text-body-sm">
                      {`${numeral(totalChange).format('0,0')}`}
                    </Typography>
                  ) : (
                    <Typography
                      className="text-gray-600"
                      variant="text-body-sm"
                    >
                      {`${numeral(totalChange).format('0,0')}`}
                    </Typography>
                  )}
                </td>
              </tr>
            </tfoot>
          )}
        </table>
      </div>
    </div>
  );
};

export default TradingActivityTable;
