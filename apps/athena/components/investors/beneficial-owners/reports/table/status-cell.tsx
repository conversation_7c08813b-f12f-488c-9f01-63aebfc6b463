import { FC } from 'react';
import { Badge, Tooltip } from '@ds';
import {
  EllipsisHorizontalIcon,
  XMarkIcon,
} from '@heroicons/react-v2/24/outline';
import { CheckIcon } from '@heroicons/react-v2/24/solid';
import { BeneficialOwnersReportStages } from '@/apollo/generated';
import {
  ParticipantWithRank,
  useBoReportTableContext,
} from '@/components/investors/beneficial-owners/reports/report-context';

/*
https://linear.app/investorhub/issue/CRM-1181/make-sure-the-status-on-athena-report-is-in-progress-up-until-the

We shouldn't show "done" status for the Athena Report until all layers are complete. The status should reflect the completion of the report in stages, not just the first layer.
*/

interface Props {
  participant: ParticipantWithRank;
}

const StatusCell: FC<{ status: 'done' | 'pending' | 'failed' | string }> = ({
  status,
}) => {
  return (
    <>
      {status === 'done' ? (
        <Badge
          LeadingIcon={() => <CheckIcon className="h-3 w-3" />}
          color="green"
          size="xs"
        >
          Done
        </Badge>
      ) : status === 'pending' ? (
        <Badge
          LeadingIcon={() => <EllipsisHorizontalIcon className="h-3 w-3" />}
          color="amber"
          size="xs"
        >
          In progress
        </Badge>
      ) : status === 'failed' ? (
        <Tooltip content="Unmasking failed - no response from registered holders.">
          <Badge
            LeadingIcon={() => <XMarkIcon className="h-3 w-3" />}
            color="gray"
            size="xs"
          >
            Failed
          </Badge>
        </Tooltip>
      ) : (
        <Tooltip content="Not unmasking this registered holder. This may be due to previous failed attempts and / or legal restrictions.">
          <Badge color="gray" size="xs">
            Not unmasking
          </Badge>
        </Tooltip>
      )}
    </>
  );
};

const ReportStatusCellNominee: FC<Props> = ({ participant }) => {
  /*
  Mark all nom rows as "done" if report is marked as "done" (second stage complete) and if the row isn't marked as "failed". Otherwise show "in progress"

  For any noms marked as "Failed", show failed status regardless of completion stage
  */
  const { reportStage } = useBoReportTableContext();

  const status =
    participant.status === 'failed'
      ? 'failed'
      : reportStage === BeneficialOwnersReportStages.Done
      ? 'done'
      : 'pending';

  return <StatusCell status={status} />;
};

const ReportStatusCellRegistry: FC<Props> = ({ participant }) => {
  // Any registry level item marked as owner, should always be done

  const status = participant.type === 'owner' ? 'done' : participant.status;

  return <StatusCell status={status} />;
};

const ReportStatusCellAsic: FC<Props> = ({ participant }) => {
  // Any ASIC items, marked done, should always be done

  return <StatusCell status={participant.status} />;
};

const ReportStatusCell: FC<Props> = ({ participant }) => {
  return participant.type === 'nominee' ? (
    <ReportStatusCellNominee participant={participant} />
  ) : participant.type === 'asic' ? (
    <ReportStatusCellAsic participant={participant} />
  ) : (
    <ReportStatusCellRegistry participant={participant} />
  );
};

export default ReportStatusCell;
